import 'package:flutter/material.dart';

import '../states/item_definition_state_implementation.dart';

class ItemDefinitionBuilder extends StatelessWidget {
  const ItemDefinitionBuilder({super.key});

  @override
  Widget build(BuildContext context) {
    // final itemDefinitionState = 
    return ListenableBuilder(
      listenable: ItemDefinitionStateImplementation(
        itemRepository: ItemRepositoryImplementation(
          localStorageService: LocalStorageServiceImplementation(),
          uniqueIdentifierService: UniqueIdentifierServiceImplementation(),
        ),
        tipRepository: TipRepositoryImplementation(
          localStorageService: LocalStorageServiceImplementation(),
        ),
      ),
      builder: (context, child) {
        return const Text('Item Definition');
      },
    );
  }
}