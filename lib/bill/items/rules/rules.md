Rules

The item definition step shows a list of items, a tip, a bill total, a button to add an item and a button to continue to the next step.
Starts with the stored bill, if there is one, otherwise, starts with the default one.

List of items
The default list starts with one item.
There is always a button to add a new item.
When a new item is added, it is included at the bottom of the list, below the last item.
When a new item is added, its order is automatically assigned based on its position.
When a new item is added, the label is focused, so it can be quickly edited.
When an item is deleted, the order of the remaining items are updated. The tip amount is updated. The bill total is updated.

Item
When an item is created, it starts with a blank label, 0.00 price, 1 quantity and 0.00 total.
The order of an item is assigned automatically and cannot be edited.
The label of an item can be edited as free text.
The price of an item can be edited as currency. When changed, the total is updated: total = price x quantity. The bill total is also updated.
The quantity of an item can be edited as a positive integer. When changed, the total is updated: total = price x quantity. The bill total is also updated.
The total of an item can be edited as currency. When changed, the price is updated: price = total / quantity. The bill total is updated.
There is a button to delete the item, only if there is more than one in the list.

Tip
The default tip starts with 10 percentage and 0.00 amount.
The tip stays separate from the list.
The tip has a fixed label.
The percentage of the tip can be edited as a non-negative integer. When changed, the amount is updated: amount = sum of item totals * (percentage / 100). The bill total is also updated.
The amount of the tip can be edited as currency. When changed, the percentage is updated: percentage = (amount / sum of item totals) * 100. The bill total is also updated.
There is a button to delete the tip.
When there is no tip, there is a button to add it.

Bill total
The total is always visible.
The total shows two lines of text.
The total cannot be edited.
If there is a tip, the first line shows the sum of item totals and the tip amount like "$123.45 + $6.78, and the second line shows the bill total.
If there is no tip, the first line shows the word "Total" and the second line shows the bill total.
The bill total shows a value as currency: bill total = sum of item totals + tip amount.

Continue to next step
The button to continue to the next step is always visible.
When the continue button is pressed and the sum of item totals is 0.00, an error is displayed and the user stays on this step.
When the continue button is pressed and the sum of item totals is greater than 0.00, the user continues to the next step.