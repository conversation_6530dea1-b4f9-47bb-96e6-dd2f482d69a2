import '../models/item.dart';
import '../models/tip.dart';

abstract class ItemDefinitionState {
  List<Item> get itemList;
  Future<void> updateItemName({required String itemId, required String name});
  Future<void> updateItemPrice({required String itemId, required double price});
  Future<void> updateItemQuantity({required String itemId, required int quantity});
  Future<void> updateItemTotal({required String itemId, required double total});
  Future<void> addItem();
  Future<void> removeItem(String itemId);

  Tip? get tip;
  Future<void> updateTipPercentage(int percentage);
  Future<void> updateTipAmount(double amount);
  Future<void> addTip();
  Future<void> removeTip();

  void continueToNextStep();
}
