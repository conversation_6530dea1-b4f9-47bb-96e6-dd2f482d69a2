import 'package:flutter/foundation.dart';

import '../models/item.dart';
import '../models/tip.dart';
import '../repositories/item_repository.dart';
import '../repositories/tip_repository.dart';
import 'item_definition_state.dart';

class ItemDefinitionStateImplementation extends ChangeNotifier
    implements ItemDefinitionState {
  ItemDefinitionStateImplementation({
    required ItemRepository itemRepository,
    required TipRepository tipRepository,
  }) : _itemRepository = itemRepository,
       _tipRepository = tipRepository {
    _loadItemList();
    _loadTip();
  }

  final ItemRepository _itemRepository;
  final TipRepository _tipRepository;

  List<Item> _itemList = [];
  @override
  List<Item> get itemList => _itemList;
  double get _itemTotalSum => _itemList.fold(0.0, (total, item) => total + item.total);

  Tip? _tip;
  @override
  Tip? get tip => _tip;

  Future<void> _loadItemList() async {
    _itemList = await _itemRepository.getItemList();
    if (itemList.isEmpty) await addItem();
    notifyListeners();
  }

  Future<void> _loadTip() async {
    final didInitializeTip = await _tipRepository
        .checkIfTipHasBeenInitialized();
    if (didInitializeTip) {
      _tip = await _tipRepository.getTip();
    } else {
      await addTip();
    }
    notifyListeners();
  }

  Item _getItem(String itemId) {
    return _itemList.firstWhere((item) => item.id == itemId);
  }

  @override
  Future<void> updateItemName({required String itemId, required String name}) async {
    final item = _getItem(itemId);
    final updatedItem = item.copyWith(name: name);
    _itemList = await _itemRepository.updateItem(updatedItem);
    notifyListeners();
  }

  @override
  Future<void> updateItemPrice({
    required String itemId,
    required double price,
  }) async {
    final item = _getItem(itemId);
    final total = price * item.quantity;
    final updatedItem = item.copyWith(price: price, total: total);
    _itemList = await _itemRepository.updateItem(updatedItem);
    await _updateTipAmountIfPresent();
    notifyListeners();
  }

  @override
  Future<void> updateItemQuantity({
    required String itemId,
    required int quantity,
  }) async {
    final item = _getItem(itemId);
    final total = item.price * quantity;
    final updatedItem = item.copyWith(quantity: quantity, total: total);
    _itemList = await _itemRepository.updateItem(updatedItem);
    await _updateTipAmountIfPresent();
    notifyListeners();
  }

  @override
  Future<void> updateItemTotal({
    required String itemId,
    required double total,
  }) async {
    final item = _getItem(itemId);
    final price = item.quantity > 0 ? total / item.quantity : 0.0;
    final updatedItem = item.copyWith(price: price, total: total);
    _itemList = await _itemRepository.updateItem(updatedItem);
    await _updateTipAmountIfPresent();
    notifyListeners();
  }

  @override
  Future<void> addItem() async {
    _itemList = await _itemRepository.createItem();
    await _updateTipAmountIfPresent();
    notifyListeners();
  }

  @override
  Future<void> removeItem(String itemId) async {
    _itemList = await _itemRepository.deleteItem(itemId);
    await _updateTipAmountIfPresent();
    notifyListeners();
  }

  double _calculateTipAmount(int percentage) {
    final amount = _itemTotalSum * (percentage / 100);
    return amount;
  }

  int _calculateTipPercentage(double amount) {
    final percentage = (_itemTotalSum > 0) ? ((amount / _itemTotalSum) * 100).round() : 0;
    return percentage;
  }

  Future<void> _updateTipAmountIfPresent() async {
    if (_tip != null) {
      final newAmount = _calculateTipAmount(_tip!.percentage);
      final updatedTip = _tip!.copyWith(amount: newAmount);
      await _tipRepository.updateTip(updatedTip);
      _tip = updatedTip;
    }
  }

  @override
  Future<void> updateTipPercentage(int percentage) async {
    final amount = _calculateTipAmount(percentage);
    final updatedTip = _tip!.copyWith(percentage: percentage, amount: amount);
    await _tipRepository.updateTip(updatedTip);
    _tip = updatedTip;
    notifyListeners();
  }

  @override
  Future<void> updateTipAmount(double amount) async {
    final percentage = _calculateTipPercentage(amount);
    final updatedTip = _tip!.copyWith(percentage: percentage, amount: amount);
    await _tipRepository.updateTip(updatedTip);
    _tip = updatedTip;
    notifyListeners();
  }

  @override
  Future<void> addTip() async {
    _tip = await _tipRepository.createTip();
    notifyListeners();
  }

  @override
  Future<void> removeTip() async {
    await _tipRepository.deleteTip();
    _tip = null;
    notifyListeners();
  }

  @override
  void continueToNextStep() {
    throw UnimplementedError();
  }
}
