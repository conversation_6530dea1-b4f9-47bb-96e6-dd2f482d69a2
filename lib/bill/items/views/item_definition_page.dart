import 'package:flutter/material.dart';

import '../states/item_definition_state.dart';

class ItemDefinitionPage extends StatelessWidget {
  const ItemDefinitionPage({super.key, required ItemDefinitionState state})
    : _state = state;

  final ItemDefinitionState _state;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('Item Definition')),
      body: Column(
        children: [
          // Items
          // TODO: Create ItemList widget

          // Tip
          if (_state.tip != null)
            Text('Tip: \$${_state.tip!.amount} (${_state.tip!.percentage}%)'),

          // Add tip button
          if (_state.tip == null)
            TextButton(
              onPressed: () {
                _state.addTip();
              },
              child: const Text('Add Tip'),
            )
          // Remove tip button
          else
            TextButton(
              onPressed: () {
                _state.removeTip();
              },
              child: const Text('Remove Tip'),
            ),
        ],
      ),
    );
  }
}
