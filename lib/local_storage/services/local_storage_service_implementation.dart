import 'dart:convert';

import 'package:flutter_secure_storage/flutter_secure_storage.dart';

import '../models/local_storage_key.dart';
import 'local_storage_service.dart';

class LocalStorageServiceImplementation implements LocalStorageService {
  LocalStorageServiceImplementation({FlutterSecureStorage? storage}) {
    if (storage != null) {
      _storage = storage;
    } else {
      final androidOptions = AndroidOptions(encryptedSharedPreferences: true);
      _storage = FlutterSecureStorage(aOptions: androidOptions);
    }
  }

  late final FlutterSecureStorage _storage;

  /// Serializes a value to JSON string
  String _serialize<T>(T value) {
    return json.encode({'value': value});
  }

  /// Deserializes a JSON string to the original value
  T? _deserialize<T>(String? jsonString) {
    if (jsonString == null) return null;
    final data = json.decode(jsonString) as Map<String, dynamic>;
    return data['value'] as T;
  }

  @override
  Future<void> setValue<T>(LocalStorageKey key, T value) {
    return _storage.write(key: key.name, value: _serialize<T>(value));
  }

  @override
  Future<T?> getValue<T>(LocalStorageKey key) async {
    final value = await _storage.read(key: key.name);
    return _deserialize<T>(value);
  }

  @override
  Future<bool> hasKey(LocalStorageKey key) {
    return _storage.containsKey(key: key.name);
  }

  @override
  Future<void> removeValue(LocalStorageKey key) {
    return _storage.delete(key: key.name);
  }

  @override
  Future<void> clear() {
    return _storage.deleteAll();
  }
}
