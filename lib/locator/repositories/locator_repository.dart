abstract class LocatorRepository {
  /// Register a singleton instance
  void registerSingleton<T extends Object>(T instance);

  /// Register a factory that creates new instances
  void registerFactory<T extends Object>(T Function() factory);

  /// Register a lazy singleton that is created on first use
  void registerLazySingleton<T extends Object>(T Function() factory);

  /// Get an instance of type T
  T get<T extends Object>();

  /// Check if type T is registered
  bool isRegistered<T extends Object>();
}
