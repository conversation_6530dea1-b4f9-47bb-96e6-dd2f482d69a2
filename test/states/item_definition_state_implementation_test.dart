import 'package:bill_splitter_2/bill/items/models/item.dart';
import 'package:bill_splitter_2/bill/items/models/tip.dart';
import 'package:bill_splitter_2/bill/items/repositories/item_repository.dart';
import 'package:bill_splitter_2/bill/items/repositories/tip_repository.dart';
import 'package:bill_splitter_2/bill/items/states/item_definition_state_implementation.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

class MockItemRepository extends Mock implements ItemRepository {}

class MockTipRepository extends Mock implements TipRepository {}

void main() {
  late MockItemRepository mockItemRepository;
  late MockTipRepository mockTipRepository;
  late ItemDefinitionStateImplementation state;

  setUpAll(() {
    registerFallbackValue(Tip());
    registerFallbackValue(Item(id: 'itemId'));
  });

  setUp(() {
    mockItemRepository = MockItemRepository();
    mockTipRepository = MockTipRepository();
  });

  group('ItemDefinitionStateImplementation', () {
    group('Initialization', () {
      test('creates default item when no items exist in storage', () async {
        // Arrange
        when(() => mockItemRepository.getItemList())
            .thenAnswer((_) async => []);
        when(() => mockItemRepository.createItem())
            .thenAnswer((_) async => [Item(id: 'new-item-id')]);
        when(() => mockTipRepository.checkIfTipHasBeenInitialized())
            .thenAnswer((_) async => false);
        when(() => mockTipRepository.createTip())
            .thenAnswer((_) async => Tip());

        // Act
        state = ItemDefinitionStateImplementation(
          itemRepository: mockItemRepository,
          tipRepository: mockTipRepository,
        );
        await Future.delayed(Duration.zero); // Allow async initialization

        // Assert
        expect(state.itemList, hasLength(1));
        expect(state.itemList.first.id, 'new-item-id');
        verify(() => mockItemRepository.createItem()).called(1);
      });

      test('loads existing items from storage', () async {
        // Arrange
        final existingItems = [
          Item(id: '1', name: 'Item 1'),
          Item(id: '2', name: 'Item 2'),
        ];
        when(() => mockItemRepository.getItemList())
            .thenAnswer((_) async => existingItems);
        when(() => mockTipRepository.checkIfTipHasBeenInitialized())
            .thenAnswer((_) async => false);
        when(() => mockTipRepository.createTip())
            .thenAnswer((_) async => Tip());

        // Act
        state = ItemDefinitionStateImplementation(
          itemRepository: mockItemRepository,
          tipRepository: mockTipRepository,
        );
        await Future.delayed(Duration.zero); // Allow async initialization

        // Assert
        expect(state.itemList, hasLength(2));
        expect(state.itemList[0].id, '1');
        expect(state.itemList[1].id, '2');
        verifyNever(() => mockItemRepository.createItem());
      });

      test('creates default tip when tip has not been initialized', () async {
        // Arrange
        when(() => mockItemRepository.getItemList())
            .thenAnswer((_) async => [Item(id: 'existing-item')]);
        when(() => mockTipRepository.checkIfTipHasBeenInitialized())
            .thenAnswer((_) async => false);
        when(() => mockTipRepository.createTip())
            .thenAnswer((_) async => Tip(percentage: 15));

        // Act
        state = ItemDefinitionStateImplementation(
          itemRepository: mockItemRepository,
          tipRepository: mockTipRepository,
        );
        await Future.delayed(Duration.zero); // Allow async initialization

        // Assert
        expect(state.tip, isNotNull);
        expect(state.tip!.percentage, 15);
        verify(() => mockTipRepository.createTip()).called(1);
      });

      test('loads existing tip from storage', () async {
        // Arrange
        final existingTip = Tip(percentage: 20, amount: 5.0);
        when(() => mockItemRepository.getItemList())
            .thenAnswer((_) async => [Item(id: 'existing-item')]);
        when(() => mockTipRepository.checkIfTipHasBeenInitialized())
            .thenAnswer((_) async => true);
        when(() => mockTipRepository.getTip())
            .thenAnswer((_) async => existingTip);

        // Act
        state = ItemDefinitionStateImplementation(
          itemRepository: mockItemRepository,
          tipRepository: mockTipRepository,
        );
        await Future.delayed(Duration.zero); // Allow async initialization

        // Assert
        expect(state.tip, isNotNull);
        expect(state.tip!.percentage, 20);
        expect(state.tip!.amount, 5.0);
        verifyNever(() => mockTipRepository.createTip());
      });

      test('handles case when tip was deleted (null tip)', () async {
        // Arrange
        when(() => mockItemRepository.getItemList())
            .thenAnswer((_) async => [Item(id: 'existing-item')]);
        when(() => mockTipRepository.checkIfTipHasBeenInitialized())
            .thenAnswer((_) async => true);
        when(() => mockTipRepository.getTip())
            .thenAnswer((_) async => null);

        // Act
        state = ItemDefinitionStateImplementation(
          itemRepository: mockItemRepository,
          tipRepository: mockTipRepository,
        );
        await Future.delayed(Duration.zero); // Allow async initialization

        // Assert
        expect(state.tip, isNull);
        verifyNever(() => mockTipRepository.createTip());
      });
    });

    group('updateItemPrice', () {
      test('updates item price and recalculates total', () async {
        // Arrange
        final existingItems = [
          Item(id: '1', name: 'Item 1', price: 10.0, quantity: 2, total: 20.0),
        ];
        when(() => mockItemRepository.getItemList())
            .thenAnswer((_) async => existingItems);
        when(() => mockItemRepository.updateItem(any()))
            .thenAnswer((_) async => [
              Item(id: '1', name: 'Item 1', price: 15.0, quantity: 2, total: 30.0),
            ]);
        when(() => mockTipRepository.checkIfTipHasBeenInitialized())
            .thenAnswer((_) async => false);
        when(() => mockTipRepository.createTip())
            .thenAnswer((_) async => Tip());
        when(() => mockTipRepository.updateTip(any()))
            .thenAnswer((_) async {});

        state = ItemDefinitionStateImplementation(
          itemRepository: mockItemRepository,
          tipRepository: mockTipRepository,
        );
        await Future.delayed(Duration.zero);

        // Act
        await state.updateItemPrice(itemId: '1', price: 15.0);

        // Assert
        expect(state.itemList.first.price, 15.0);
        expect(state.itemList.first.total, 30.0);
        verify(() => mockItemRepository.updateItem(any())).called(1);
      });

      test('updates tip amount when item price changes and tip is present', () async {
        // Arrange
        final existingItems = [
          Item(id: '1', name: 'Item 1', price: 10.0, quantity: 2, total: 20.0),
        ];
        final existingTip = Tip(percentage: 15, amount: 3.0); // 15% of $20
        when(() => mockItemRepository.getItemList())
            .thenAnswer((_) async => existingItems);
        when(() => mockItemRepository.updateItem(any()))
            .thenAnswer((_) async => [
              Item(id: '1', name: 'Item 1', price: 15.0, quantity: 2, total: 30.0),
            ]);
        when(() => mockTipRepository.checkIfTipHasBeenInitialized())
            .thenAnswer((_) async => true);
        when(() => mockTipRepository.getTip())
            .thenAnswer((_) async => existingTip);
        when(() => mockTipRepository.updateTip(any()))
            .thenAnswer((_) async {});

        state = ItemDefinitionStateImplementation(
          itemRepository: mockItemRepository,
          tipRepository: mockTipRepository,
        );
        await Future.delayed(Duration.zero);

        // Act
        await state.updateItemPrice(itemId: '1', price: 15.0);

        // Assert
        expect(state.itemList.first.total, 30.0);
        expect(state.tip!.percentage, 15); // Percentage stays the same
        expect(state.tip!.amount, 4.5); // 15% of new total $30
        verify(() => mockItemRepository.updateItem(any())).called(1);
        verify(() => mockTipRepository.updateTip(any())).called(1);
      });
    });

    group('updateItemName', () {
      test('updates item name', () async {
        // Arrange
        final existingItems = [
          Item(id: '1', name: 'Old Name', price: 10.0, quantity: 1, total: 10.0),
        ];
        when(() => mockItemRepository.getItemList())
            .thenAnswer((_) async => existingItems);
        when(() => mockItemRepository.updateItem(any()))
            .thenAnswer((_) async => [
              Item(id: '1', name: 'New Name', price: 10.0, quantity: 1, total: 10.0),
            ]);
        when(() => mockTipRepository.checkIfTipHasBeenInitialized())
            .thenAnswer((_) async => false);
        when(() => mockTipRepository.createTip())
            .thenAnswer((_) async => Tip());
        when(() => mockTipRepository.updateTip(any()))
            .thenAnswer((_) async {});

        state = ItemDefinitionStateImplementation(
          itemRepository: mockItemRepository,
          tipRepository: mockTipRepository,
        );
        await Future.delayed(Duration.zero);

        // Act
        await state.updateItemName(itemId: '1', name: 'New Name');

        // Assert
        expect(state.itemList.first.name, 'New Name');
        expect(state.itemList.first.price, 10.0); // Other fields unchanged
        expect(state.itemList.first.quantity, 1);
        expect(state.itemList.first.total, 10.0);
        verify(() => mockItemRepository.updateItem(any())).called(1);
      });

      test('does not update tip when item name changes', () async {
        // Arrange
        final existingItems = [
          Item(id: '1', name: 'Old Name', price: 10.0, quantity: 1, total: 10.0),
        ];
        final existingTip = Tip(percentage: 15, amount: 1.5);
        when(() => mockItemRepository.getItemList())
            .thenAnswer((_) async => existingItems);
        when(() => mockItemRepository.updateItem(any()))
            .thenAnswer((_) async => [
              Item(id: '1', name: 'New Name', price: 10.0, quantity: 1, total: 10.0),
            ]);
        when(() => mockTipRepository.checkIfTipHasBeenInitialized())
            .thenAnswer((_) async => true);
        when(() => mockTipRepository.getTip())
            .thenAnswer((_) async => existingTip);

        state = ItemDefinitionStateImplementation(
          itemRepository: mockItemRepository,
          tipRepository: mockTipRepository,
        );
        await Future.delayed(Duration.zero);

        // Act
        await state.updateItemName(itemId: '1', name: 'New Name');

        // Assert
        expect(state.itemList.first.name, 'New Name');
        expect(state.tip!.percentage, 15); // Tip unchanged
        expect(state.tip!.amount, 1.5); // Tip unchanged
        verify(() => mockItemRepository.updateItem(any())).called(1);
        verifyNever(() => mockTipRepository.updateTip(any())); // Tip should not be updated
      });
    });

    group('updateItemQuantity', () {
      test('updates item quantity and recalculates total', () async {
        // Arrange
        final existingItems = [
          Item(id: '1', name: 'Item 1', price: 10.0, quantity: 2, total: 20.0),
        ];
        when(() => mockItemRepository.getItemList())
            .thenAnswer((_) async => existingItems);
        when(() => mockItemRepository.updateItem(any()))
            .thenAnswer((_) async => [
              Item(id: '1', name: 'Item 1', price: 10.0, quantity: 3, total: 30.0),
            ]);
        when(() => mockTipRepository.checkIfTipHasBeenInitialized())
            .thenAnswer((_) async => false);
        when(() => mockTipRepository.createTip())
            .thenAnswer((_) async => Tip());
        when(() => mockTipRepository.updateTip(any()))
            .thenAnswer((_) async {});

        state = ItemDefinitionStateImplementation(
          itemRepository: mockItemRepository,
          tipRepository: mockTipRepository,
        );
        await Future.delayed(Duration.zero);

        // Act
        await state.updateItemQuantity(itemId: '1', quantity: 3);

        // Assert
        expect(state.itemList.first.quantity, 3);
        expect(state.itemList.first.total, 30.0);
        verify(() => mockItemRepository.updateItem(any())).called(1);
      });

      test('updates tip amount when item quantity changes and tip is present', () async {
        // Arrange
        final existingItems = [
          Item(id: '1', name: 'Item 1', price: 10.0, quantity: 2, total: 20.0),
        ];
        final existingTip = Tip(percentage: 20, amount: 4.0); // 20% of $20
        when(() => mockItemRepository.getItemList())
            .thenAnswer((_) async => existingItems);
        when(() => mockItemRepository.updateItem(any()))
            .thenAnswer((_) async => [
              Item(id: '1', name: 'Item 1', price: 10.0, quantity: 3, total: 30.0),
            ]);
        when(() => mockTipRepository.checkIfTipHasBeenInitialized())
            .thenAnswer((_) async => true);
        when(() => mockTipRepository.getTip())
            .thenAnswer((_) async => existingTip);
        when(() => mockTipRepository.updateTip(any()))
            .thenAnswer((_) async {});

        state = ItemDefinitionStateImplementation(
          itemRepository: mockItemRepository,
          tipRepository: mockTipRepository,
        );
        await Future.delayed(Duration.zero);

        // Act
        await state.updateItemQuantity(itemId: '1', quantity: 3);

        // Assert
        expect(state.itemList.first.total, 30.0);
        expect(state.tip!.percentage, 20); // Percentage stays the same
        expect(state.tip!.amount, 6.0); // 20% of new total $30
        verify(() => mockItemRepository.updateItem(any())).called(1);
        verify(() => mockTipRepository.updateTip(any())).called(1);
      });
    });

    group('updateItemTotal', () {
      test('updates item total and recalculates price', () async {
        // Arrange
        final existingItems = [
          Item(id: '1', name: 'Item 1', price: 10.0, quantity: 2, total: 20.0),
        ];
        when(() => mockItemRepository.getItemList())
            .thenAnswer((_) async => existingItems);
        when(() => mockItemRepository.updateItem(any()))
            .thenAnswer((_) async => [
              Item(id: '1', name: 'Item 1', price: 15.0, quantity: 2, total: 30.0),
            ]);
        when(() => mockTipRepository.checkIfTipHasBeenInitialized())
            .thenAnswer((_) async => false);
        when(() => mockTipRepository.createTip())
            .thenAnswer((_) async => Tip());
        when(() => mockTipRepository.updateTip(any()))
            .thenAnswer((_) async {});

        state = ItemDefinitionStateImplementation(
          itemRepository: mockItemRepository,
          tipRepository: mockTipRepository,
        );
        await Future.delayed(Duration.zero);

        // Act
        await state.updateItemTotal(itemId: '1', total: 30.0);

        // Assert
        expect(state.itemList.first.total, 30.0);
        expect(state.itemList.first.price, 15.0);
        verify(() => mockItemRepository.updateItem(any())).called(1);
      });

      test('updates tip amount when item total changes and tip is present', () async {
        // Arrange
        final existingItems = [
          Item(id: '1', name: 'Item 1', price: 10.0, quantity: 2, total: 20.0),
        ];
        final existingTip = Tip(percentage: 10, amount: 2.0); // 10% of $20
        when(() => mockItemRepository.getItemList())
            .thenAnswer((_) async => existingItems);
        when(() => mockItemRepository.updateItem(any()))
            .thenAnswer((_) async => [
              Item(id: '1', name: 'Item 1', price: 15.0, quantity: 2, total: 30.0),
            ]);
        when(() => mockTipRepository.checkIfTipHasBeenInitialized())
            .thenAnswer((_) async => true);
        when(() => mockTipRepository.getTip())
            .thenAnswer((_) async => existingTip);
        when(() => mockTipRepository.updateTip(any()))
            .thenAnswer((_) async {});

        state = ItemDefinitionStateImplementation(
          itemRepository: mockItemRepository,
          tipRepository: mockTipRepository,
        );
        await Future.delayed(Duration.zero);

        // Act
        await state.updateItemTotal(itemId: '1', total: 30.0);

        // Assert
        expect(state.itemList.first.total, 30.0);
        expect(state.tip!.percentage, 10); // Percentage stays the same
        expect(state.tip!.amount, 3.0); // 10% of new total $30
        verify(() => mockItemRepository.updateItem(any())).called(1);
        verify(() => mockTipRepository.updateTip(any())).called(1);
      });
    });

    group('addItem', () {
      test('adds new item to the list', () async {
        // Arrange
        final existingItems = [Item(id: '1', name: 'Item 1')];
        final updatedItems = [
          Item(id: '1', name: 'Item 1'),
          Item(id: '2', name: 'Item 2'),
        ];
        when(() => mockItemRepository.getItemList())
            .thenAnswer((_) async => existingItems);
        when(() => mockItemRepository.createItem())
            .thenAnswer((_) async => updatedItems);
        when(() => mockTipRepository.checkIfTipHasBeenInitialized())
            .thenAnswer((_) async => false);
        when(() => mockTipRepository.createTip())
            .thenAnswer((_) async => Tip());
        when(() => mockTipRepository.updateTip(any()))
            .thenAnswer((_) async {});

        state = ItemDefinitionStateImplementation(
          itemRepository: mockItemRepository,
          tipRepository: mockTipRepository,
        );
        await Future.delayed(Duration.zero);

        // Act
        await state.addItem();

        // Assert
        expect(state.itemList, hasLength(2));
        expect(state.itemList[1].id, '2');
        verify(() => mockItemRepository.createItem()).called(1); // Only called during addItem (not during init since list wasn't empty)
      });

      test('updates tip amount when new item is added and tip is present', () async {
        // Arrange
        final existingItems = [Item(id: '1', name: 'Item 1', price: 10.0, quantity: 1, total: 10.0)];
        final updatedItems = [
          Item(id: '1', name: 'Item 1', price: 10.0, quantity: 1, total: 10.0),
          Item(id: '2', name: 'Item 2', price: 5.0, quantity: 1, total: 5.0),
        ];
        final existingTip = Tip(percentage: 15, amount: 1.5); // 15% of $10
        when(() => mockItemRepository.getItemList())
            .thenAnswer((_) async => existingItems);
        when(() => mockItemRepository.createItem())
            .thenAnswer((_) async => updatedItems);
        when(() => mockTipRepository.checkIfTipHasBeenInitialized())
            .thenAnswer((_) async => true);
        when(() => mockTipRepository.getTip())
            .thenAnswer((_) async => existingTip);
        when(() => mockTipRepository.updateTip(any()))
            .thenAnswer((_) async {});

        state = ItemDefinitionStateImplementation(
          itemRepository: mockItemRepository,
          tipRepository: mockTipRepository,
        );
        await Future.delayed(Duration.zero);

        // Act
        await state.addItem();

        // Assert
        expect(state.itemList, hasLength(2));
        expect(state.tip!.percentage, 15); // Percentage stays the same
        expect(state.tip!.amount, 2.25); // 15% of new total $15 ($10 + $5)
        verify(() => mockItemRepository.createItem()).called(1);
        verify(() => mockTipRepository.updateTip(any())).called(1);
      });
    });

    group('removeItem', () {
      test('removes item from the list', () async {
        // Arrange
        final existingItems = [
          Item(id: '1', name: 'Item 1'),
          Item(id: '2', name: 'Item 2'),
        ];
        final updatedItems = [Item(id: '1', name: 'Item 1')];
        when(() => mockItemRepository.getItemList())
            .thenAnswer((_) async => existingItems);
        when(() => mockItemRepository.deleteItem('2'))
            .thenAnswer((_) async => updatedItems);
        when(() => mockTipRepository.checkIfTipHasBeenInitialized())
            .thenAnswer((_) async => false);
        when(() => mockTipRepository.createTip())
            .thenAnswer((_) async => Tip());
        when(() => mockTipRepository.updateTip(any()))
            .thenAnswer((_) async {});

        state = ItemDefinitionStateImplementation(
          itemRepository: mockItemRepository,
          tipRepository: mockTipRepository,
        );
        await Future.delayed(Duration.zero);

        // Act
        await state.removeItem('2');

        // Assert
        expect(state.itemList, hasLength(1));
        expect(state.itemList.first.id, '1');
        verify(() => mockItemRepository.deleteItem('2')).called(1);
      });

      test('updates tip amount when item is removed and tip is present', () async {
        // Arrange
        final existingItems = [
          Item(id: '1', name: 'Item 1', price: 10.0, quantity: 1, total: 10.0),
          Item(id: '2', name: 'Item 2', price: 5.0, quantity: 1, total: 5.0),
        ];
        final updatedItems = [Item(id: '1', name: 'Item 1', price: 10.0, quantity: 1, total: 10.0)];
        final existingTip = Tip(percentage: 20, amount: 3.0); // 20% of $15
        when(() => mockItemRepository.getItemList())
            .thenAnswer((_) async => existingItems);
        when(() => mockItemRepository.deleteItem('2'))
            .thenAnswer((_) async => updatedItems);
        when(() => mockTipRepository.checkIfTipHasBeenInitialized())
            .thenAnswer((_) async => true);
        when(() => mockTipRepository.getTip())
            .thenAnswer((_) async => existingTip);
        when(() => mockTipRepository.updateTip(any()))
            .thenAnswer((_) async {});

        state = ItemDefinitionStateImplementation(
          itemRepository: mockItemRepository,
          tipRepository: mockTipRepository,
        );
        await Future.delayed(Duration.zero);

        // Act
        await state.removeItem('2');

        // Assert
        expect(state.itemList, hasLength(1));
        expect(state.tip!.percentage, 20); // Percentage stays the same
        expect(state.tip!.amount, 2.0); // 20% of new total $10
        verify(() => mockItemRepository.deleteItem('2')).called(1);
        verify(() => mockTipRepository.updateTip(any())).called(1);
      });

      test('throws StateError when trying to remove the only item', () async {
        // Arrange
        final existingItems = [Item(id: '1', name: 'Only Item')];
        when(() => mockItemRepository.getItemList())
            .thenAnswer((_) async => existingItems);
        when(() => mockItemRepository.deleteItem('1'))
            .thenThrow(StateError('Cannot delete the only item in the list'));
        when(() => mockTipRepository.checkIfTipHasBeenInitialized())
            .thenAnswer((_) async => false);
        when(() => mockTipRepository.createTip())
            .thenAnswer((_) async => Tip());

        state = ItemDefinitionStateImplementation(
          itemRepository: mockItemRepository,
          tipRepository: mockTipRepository,
        );
        await Future.delayed(Duration.zero);

        // Act & Assert
        expect(
          () => state.removeItem('1'),
          throwsA(isA<StateError>().having(
            (e) => e.message,
            'message',
            'Cannot delete the only item in the list',
          )),
        );
      });

      test('throws ArgumentError when trying to remove non-existent item', () async {
        // Arrange
        final existingItems = [
          Item(id: '1', name: 'Item 1'),
          Item(id: '2', name: 'Item 2'),
        ];
        when(() => mockItemRepository.getItemList())
            .thenAnswer((_) async => existingItems);
        when(() => mockItemRepository.deleteItem('non-existent'))
            .thenThrow(ArgumentError('Item with id "non-existent" not found'));
        when(() => mockTipRepository.checkIfTipHasBeenInitialized())
            .thenAnswer((_) async => false);
        when(() => mockTipRepository.createTip())
            .thenAnswer((_) async => Tip());

        state = ItemDefinitionStateImplementation(
          itemRepository: mockItemRepository,
          tipRepository: mockTipRepository,
        );
        await Future.delayed(Duration.zero);

        // Act & Assert
        expect(
          () => state.removeItem('non-existent'),
          throwsA(isA<ArgumentError>().having(
            (e) => e.message,
            'message',
            'Item with id "non-existent" not found',
          )),
        );
      });
    });

    group('addTip', () {
      test('adds new tip', () async {
        // Arrange
        when(() => mockItemRepository.getItemList())
            .thenAnswer((_) async => [Item(id: '1')]);
        when(() => mockTipRepository.checkIfTipHasBeenInitialized())
            .thenAnswer((_) async => true);
        when(() => mockTipRepository.getTip())
            .thenAnswer((_) async => null); // Tip was deleted
        when(() => mockTipRepository.createTip())
            .thenAnswer((_) async => Tip(percentage: 15));

        state = ItemDefinitionStateImplementation(
          itemRepository: mockItemRepository,
          tipRepository: mockTipRepository,
        );
        await Future.delayed(Duration.zero);

        // Act
        await state.addTip();

        // Assert
        expect(state.tip, isNotNull);
        expect(state.tip!.percentage, 15);
        verify(() => mockTipRepository.createTip()).called(1);
      });
    });

    group('updateTipPercentage', () {
      test('updates tip percentage and recalculates amount', () async {
        // Arrange
        final existingItems = [
          Item(id: '1', price: 10.0, quantity: 1, total: 10.0),
        ];
        final existingTip = Tip(percentage: 10, amount: 1.0);
        when(() => mockItemRepository.getItemList())
            .thenAnswer((_) async => existingItems);
        when(() => mockTipRepository.checkIfTipHasBeenInitialized())
            .thenAnswer((_) async => true);
        when(() => mockTipRepository.getTip())
            .thenAnswer((_) async => existingTip);
        when(() => mockTipRepository.updateTip(any()))
            .thenAnswer((_) async {});

        state = ItemDefinitionStateImplementation(
          itemRepository: mockItemRepository,
          tipRepository: mockTipRepository,
        );
        await Future.delayed(Duration.zero);

        // Act
        await state.updateTipPercentage(20);

        // Assert
        expect(state.tip!.percentage, 20);
        expect(state.tip!.amount, 2.0); // 20% of $10
        verify(() => mockTipRepository.updateTip(any())).called(1);
      });
    });

    group('updateTipAmount', () {
      test('updates tip amount and recalculates percentage', () async {
        // Arrange
        final existingItems = [
          Item(id: '1', price: 10.0, quantity: 1, total: 10.0),
        ];
        final existingTip = Tip(percentage: 10, amount: 1.0);
        when(() => mockItemRepository.getItemList())
            .thenAnswer((_) async => existingItems);
        when(() => mockTipRepository.checkIfTipHasBeenInitialized())
            .thenAnswer((_) async => true);
        when(() => mockTipRepository.getTip())
            .thenAnswer((_) async => existingTip);
        when(() => mockTipRepository.updateTip(any()))
            .thenAnswer((_) async {});

        state = ItemDefinitionStateImplementation(
          itemRepository: mockItemRepository,
          tipRepository: mockTipRepository,
        );
        await Future.delayed(Duration.zero);

        // Act
        await state.updateTipAmount(1.5);

        // Assert
        expect(state.tip!.amount, 1.5);
        expect(state.tip!.percentage, 15); // $1.5 is 15% of $10
        verify(() => mockTipRepository.updateTip(any())).called(1);
      });
    });

    group('removeTip', () {
      test('removes tip', () async {
        // Arrange
        final existingTip = Tip(percentage: 10, amount: 1.0);
        when(() => mockItemRepository.getItemList())
            .thenAnswer((_) async => [Item(id: '1')]);
        when(() => mockTipRepository.checkIfTipHasBeenInitialized())
            .thenAnswer((_) async => true);
        when(() => mockTipRepository.getTip())
            .thenAnswer((_) async => existingTip);
        when(() => mockTipRepository.deleteTip())
            .thenAnswer((_) async {});

        state = ItemDefinitionStateImplementation(
          itemRepository: mockItemRepository,
          tipRepository: mockTipRepository,
        );
        await Future.delayed(Duration.zero);

        // Act
        await state.removeTip();

        // Assert
        expect(state.tip, isNull);
        verify(() => mockTipRepository.deleteTip()).called(1);
      });
    });
  });
}
